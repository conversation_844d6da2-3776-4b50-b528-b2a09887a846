 <div class="phone-number-container">
  <ng-content select="[slot='heading']">
    <b> Phone numbers </b>
  </ng-content>

  <ng-content select="[slot='description']">
    <p>
      If entering an Australian landline phone number, you must include the area
      code.
    </p>
  </ng-content>
  <div [formGroup]="form" class="mygov-form-grid">
    @for (phoneType of phoneTypes; track phoneType) {
      <div [formGroupName]="phoneType" [class]="phoneType">
        <gui-grid variant="1up">
          <gui-checkbox
            formControlName="enabled"
            class="enabled"
            [labelText]="friendlyNames[phoneType]">
          </gui-checkbox>
          <!-- todo check guikit
        [inputAriaLabel]="'has ' + friendlyNames[phoneType]"
        -->
        @if (getControl(phoneType, 'enabled').value) {
        <div class="phone-fields">
          <!-- dialling code -->
          <gui-select
                      autocomplete="list"
                      formControlName="countryCode"
                      class="countryCode"
                      [errorText]="getErrorMessage(phoneType, 'countryCode')"
                      [inputAriaLabel]="friendlyNames[phoneType] + ' dialling code'"
                      (guiBlurred)="markControlTouched(phoneType, 'countryCode')"
                      >
            @for (c of dialingCodes; track c.dialingCode + c.countryCode) {
            <gui-option [value]="c.dialingCode">
              <div class="s">
                <div>{{ c.countryName }}</div>
                <div>
                  @if (c.dialingCode) {
                  (+{{ c.dialingCode }})
                  }
                </div>
              </div>
            </gui-option>
            }
          </gui-select>

          <!-- phone number    -->
          <gui-input
                     formControlName="number"
                     class="number"
                     type="text"
                     [errorText]="getErrorMessage(phoneType, 'number')"
                     (guiBlurred)="markControlTouched(phoneType, 'number')">
          </gui-input>
        </div>
        }
      </gui-grid>
    </div>
    }
  </div>
</div> 