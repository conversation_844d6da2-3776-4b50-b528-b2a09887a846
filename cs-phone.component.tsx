import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  forwardRef,
  DestroyRef,
  inject,
  Input,
  SimpleChanges,
  OnChanges,
  Output,
  EventEmitter,
  ChangeDetectorRef
} from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import { MygovGuiAngularModule } from '@gui/mygov-angular';
import { ReferenceDataService } from '../../services/reference-data.service';
import {
  PhoneDetails,
  PhoneNumber,
  createPhoneDetails,
  PhoneFormErrorLabels
} from './cs-phone.model';
import { DialingCodeDTO } from '@dhs/child-support-shared/dist/shared';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { debounceTime } from 'rxjs';
import {
  DEFAULT_PHONE_FORM_ERROR_LABELS,
  DEFAULT_PHONE_FORM_LABELS,
  DEFAULT_PHONE_DIALING_CODES
} from './cs-phone.constants';

@Component({
  selector: 'cs-phone',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,

    ReactiveFormsModule,
    MygovGuiAngularModule,
  ],
  templateUrl: './cs-phone.component.html',
  styleUrl: './cs-phone.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CsPhoneNumberInputComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => CsPhoneNumberInputComponent),
      multi: true,
    },
  ],
})
//, OnChanges
export class CsPhoneNumberInputComponent
  implements ControlValueAccessor, OnInit, Validator, OnChanges
{
  @Input() minRequiredPhones = 0;
  @Input() phoneData!: PhoneDetails;
  @Output() phoneChange = new EventEmitter<PhoneDetails>();
  form!: FormGroup;

  @Input() errorLabels: PhoneFormErrorLabels = DEFAULT_PHONE_FORM_ERROR_LABELS;

  @Input() friendlyNames = DEFAULT_PHONE_FORM_LABELS;

  dialingCodes: DialingCodeDTO[] = DEFAULT_PHONE_DIALING_CODES;

  DETFAULT_DIALING_CODE = '61';

  phoneTypes: (keyof PhoneDetails)[] = ['mobile', 'dayTime', 'afterHours'];

  phonePattern = /^\d+$/;

  onChange: (value: PhoneDetails | null) => void = () => { };
  onTouched: () => void = () => { };
  isDisabled = false;
  isComponentTouched = false;

  private destroyRef = inject(DestroyRef);

  constructor(
    private referenceDataService: ReferenceDataService,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef
  ) {
    this.form = this.createForm();
  }

  createForm(): FormGroup {
    return this.fb.group({
      mobile: this.createPhoneGroup(),
      dayTime: this.createPhoneGroup(),
      afterHours: this.createPhoneGroup(),
    });
  }

  createPhoneGroup(): FormGroup {
    return this.fb.group({
      enabled: [false],
      countryCode: ['', Validators.required],
      number: [
        '',
        [
          Validators.required,
          Validators.pattern(this.phonePattern),
          this.phoneNumberValidator()
        ],
      ],
    });
  }

  phoneNumberValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      const countryCode = control.parent?.get('countryCode')?.value;
      const value = control.value || '';

      if (countryCode === '61' && value.length !== 10) {
        return { australianLength: true }
      }
      if (countryCode !== '61' && value.length > 15) {
        return { internationalLength: true };
      }
      return null;
    };
  }
ngOnInit(): void {
    this.getDialingCodes();

    if (this.phoneData) {
      this.writeValue(this.phoneData);
    }

    this.form.valueChanges
      .pipe(debounceTime(200), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        const sanitized = this.getSanitizedValue();
        this.phoneChange.emit(sanitized);
        this.onChange(sanitized);
        this.onTouched();
      });
  }

  private getSanitizedValue(): PhoneDetails {
    const sanitized = createPhoneDetails();
    this.phoneTypes.forEach(phoneType => {
      const group = this.form.get(phoneType) as FormGroup;
      const enabled = group.get('enabled')?.value;
      const valid = group.valid;

      sanitized[phoneType] = {
        enabled: enabled,
        countryCode:
          enabled && valid ? group.get('countryCode')?.value : undefined,
        number: enabled && valid ? group.get('number')?.value : undefined,
      };
    });

    return sanitized;
  }

  // control value accessor implementaion
  writeValue(value: PhoneDetails | null): void {
    const val = value || createPhoneDetails();

    this.phoneTypes.forEach(phoneType => {
      const group = this.form.get(phoneType) as FormGroup;

      // if the incoming data does not have country code then preselect 61/Australia
      if (!val[phoneType]?.countryCode) {
        val[phoneType].countryCode = '61';
      }

      group.patchValue(val[phoneType], { emitEvent: false });

      group.get('countryCode')?.updateValueAndValidity();
      group.get('number')?.updateValueAndValidity();
    });
  }

  registerOnChange(fn: (value: PhoneDetails | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = () => {
      fn();
      this.isComponentTouched = true;
      this.cdr.detectChanges();
    }
  }

  setDisabledState?(isDisabled: boolean): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }
// validator
//validCount = 0;
validate(): ValidationErrors | null {
const errors: ValidationErrors = {};
let validCount = 0;

this.phoneTypes.forEach(phoneType => {
const phoneGroup = this.form.get(phoneType) as FormGroup;
const phoneValue = this.form.get(phoneType)?.value;

if (phoneValue.enabled) {
  if (phoneGroup.valid) {
    validCount++;
  } else {
    const groupErrors: ValidationErrors = {};

    const countryCodeControl = phoneGroup.get('countryCode');
    if (countryCodeControl?.errors) {
      const fieldMessages: ValidationErrors = {};
      for (const errKey in countryCodeControl.errors) {
        if (countryCodeControl.errors.hasOwnProperty(errKey)) {
          fieldMessages[errKey] = this.getMesssageForSpecificError(
            phoneType,
            'countryCode',
            errKey
          );
        }
      }
      if (Object.keys(fieldMessages).length > 0) {
        groupErrors['countryCode'] = fieldMessages;
      }
    }

    const numberControl = phoneGroup.get('number');
    if (numberControl?.errors) {
      const fieldMessages: ValidationErrors = {};
      for (const errKey in numberControl.errors) {
        if (numberControl.errors.hasOwnProperty(errKey)) {
          fieldMessages[errKey] = this.getMesssageForSpecificError(phoneType, 'number', errKey);
        }
      }
      if (Object.keys(fieldMessages).length > 0) {
        groupErrors['number'] = fieldMessages;
      }
    }

    if (Object.keys(groupErrors).length > 0) {
      errors[phoneType] = groupErrors;
    }
    phoneGroup.markAllAsTouched();
  }
}
});

if (this.minRequiredPhones > 0) {
if (validCount < this.minRequiredPhones) {
  errors['minRequired'] = {
    required: this.minRequiredPhones,
    actual: validCount,
    message: `At least ${this.minRequiredPhones} valid  phone number(s) required`,
  };
}
}
// if (validCount != this.validCount) {
//   this.validCount = validCount;
// }

return Object.keys(errors) ? errors : null;
}

  markAsTouched() {
    this.isComponentTouched = true;
    this.form.markAllAsTouched();
    this.cdr.detectChanges();
  }

  markControlTouched(phoneType: keyof PhoneDetails, field: string): void {
    const control = this.form.get(`${phoneType}.${field}`);
    if (!control) return;
    control.markAsTouched();
    this.onTouched();
    this.cdr.detectChanges();
  }

  // todo: fields friendly name
  getErrorMessage(phoneType: keyof PhoneDetails, field: string): string {
    const control = this.form.get(`${phoneType}.${field}`);
    if (!control) return '';

    if (!control.errors) return '';

    const shoudShowErrors = control.touched ||     // this input touched
      this.isComponentTouched ||     // some other part touched
      (control.dirty && this.form.touched);

    if(!shoudShowErrors)
       return '';

    //if (!control.touched) return '';

    const errors = control.errors || {};
    const isAustralia =
      this.form.get(`${phoneType}.countryCode`)?.value === '61';
    const phoneNumber = control.value;

    if (field === 'number') {
      if (errors['required']) {
        return this.errorLabels.required;
      }

      if (phoneNumber && /[^0-9]/.test(phoneNumber)) {
        return this.errorLabels.numbersOnly;
      }

      if (isAustralia) {
        if (phoneNumber.length !== 10) {
          return this.errorLabels.australianLength;
        } else {
          return this.errorLabels.australianLengthNonMobile;
        }
      } else if (phoneNumber.length > 15) {
        return this.errorLabels.internationalLength;
      }

      if (errors['pattern']) {
        return this.errorLabels.invalidFormat;
      }
    }

    if (field === 'countryCode') {
      if (errors['required']) {
        return this.errorLabels.required;
      }
    }

    return '';
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['phoneData'] && changes['phoneData'].currentValue) {
      this.writeValue(changes['phoneData'].currentValue);
      this.form.markAsTouched();
    }

    if (
      changes['minRequiedPhones'] &&
      changes['minRequiedPhones'].firstChange
    ) {
      this.onChange(this.getSanitizedValue());
    }
  }

  get formErrors() {
    return this.form.errors || '';
  }

  getControl(phoneType: keyof PhoneDetails, field: keyof PhoneNumber) {
    return this.form.get([phoneType, field]) as FormControl;
  }

  getCountryOptionLabel(c: DialingCodeDTO) {
    return `${c.countryName} (+${c.dialingCode})`;
  }

  private async getDialingCodes() {
    try {
      const dialingCodes =
        await this.referenceDataService.getDialingCodesList();
      const dialingCodesFiltered = dialingCodes
        .filter(_ => _.countryName && _.dialingCode)
        .sort((a, b) => a.countryName!.localeCompare(b.countryName));

      const blank = {
        countryCode: '',
        countryName: 'Select a dialing code',
        dialingCode: '',
      };
      this.dialingCodes = [blank, ...dialingCodesFiltered];
    } catch (error) {
      //
    }
  }
}


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['phoneData'] && changes['phoneData'].currentValue) {
      this.writeValue(changes['phoneData'].currentValue);
      this.form.markAsTouched();
    }

    if (
      changes['minRequiedPhones'] &&
      changes['minRequiedPhones'].firstChange
    ) {
      this.onChange(this.getSanitizedValue());
    }
  }

  get formErrors() {
    return this.form.errors || '';
  }

  getControl(phoneType: keyof PhoneDetails, field: keyof PhoneNumber) {
    return this.form.get([phoneType, field]) as FormControl;
  }

  getCountryOptionLabel(c: DialingCodeDTO) {
    return `${c.countryName} (+${c.dialingCode})`;
  }

  private async getDialingCodes() {
    try {
      const dialingCodes =
        await this.referenceDataService.getDialingCodesList();
      const dialingCodesFiltered = dialingCodes
        .filter(_ => _.countryName && _.dialingCode)
        .sort((a, b) => a.countryName!.localeCompare(b.countryName));

      const blank = {
        countryCode: '',
        countryName: 'Select a dialing code',
        dialingCode: '',
      };
      this.dialingCodes = [blank, ...dialingCodesFiltered];
    } catch (error) {
      //
    }
  }
}
