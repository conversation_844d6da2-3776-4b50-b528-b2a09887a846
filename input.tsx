@Component({
  tag: 'gui-input',
  styleUrl: 'input.css',
  shadow: true,
})
export class Input {
  private inputId = generateId('gui-input');
  private inputElement: HTMLInputElement;
  private searchButton: HTMLButtonElement;
  private guiSpinner: HTMLGuiSpinnerElement;

  @Element() host!: HTMLGuiInputElement;
  @State() hasFocus = false;
  @State() fieldControlData: FieldControlData;
  @State() savedFocusOnLoading: 'search' | null = null;
  /**
   * Sets the element's autofocus attribute, indicating that the control should have focus when the page loads.
   */
  @Prop() autofocus = false;

  /**
   * Input size.
   */
  @Prop({ reflect: true }) size: 'small' | 'medium' | 'large' = 'medium';

  /**
   * Input type attribute.
   */
  @Prop({ reflect: true }) type: 'text' | 'number' | 'email' | 'url' | 'date' | 'datetime-local' | 'time' | 'password' | 'search' = 'text';

  /**
   * Default value displayed in the input box.
   */
  @Prop({ reflect: true, mutable: true }) value: any;

  /**
   * Name of the component, saved as part of form data.
   */
  @Prop() name: string;

  /**
   * Text displayed in the text box before a user enters a value.
   */
  @Prop() placeholderText?: string;

  /**
   * Disables the component on the interface. If the attribute’s value is undefined, the value is set to false.
   */
  @Prop({ reflect: true }) disabled = false;

  /**
   * Controls if the error icon is displayed and defaults to true.
   */
  @Prop() errorIcon = true;

  /**
   * If `true`, the user must fill in a value before submitting a form.
   */
  @Prop() required = false;

  /**
   * This attribute indicates whether the value of the control can be automatically completed by the browser.
   */
  @Prop() autocomplete: 'on' | 'off' = 'off';

  /**
   * The maximum number of characters.
   */
  @Prop() maxlength?: number;

  /**
   * The minimum number of characters.
   */
  @Prop() minlength?: number;

  /**
   * Specifies a maximum value that can be entered.
   */
  @Prop() max?: number | string;

  /**
   * Specifies a minimum value that can be entered.
   */
  @Prop() min?: number | string;

  /**
   * The step attribute is used when the type is `number`. It specifies the interval between legal numbers in a number/decimal input element.
   * Works with the min and max attributes to limit the increments at which a value can be set.
   * Possible values are `any` or a positive floating point number.
   */
  @Prop() step = 'any';

  /**
   * Restrict a value to a specific pattern.
   */
  @Prop() pattern?: string;

  /**
   * Indicates that the user cannot modify the value of the control.
   */
  @Prop() readonly?: boolean;

  /**
   * Label displayed on the interface, for the component.
   */
  @Prop({ reflect: true }) labelText: string;

  /**
   * Heading level. Must be between 1 and 6.
   */
  @Prop({ reflect: true }) headingLevel: number | undefined;

  /**
   * Help text displayed above the text box.
   */
  @Prop({ reflect: true }) helpText: string;

  /**
   * Help text position.
   */
  @Prop({ reflect: true }) helpTextPosition: 'top' | 'bottom' = 'top';

  /**
   * Error text displayed below the text box.
   */
  @Prop({ reflect: true, mutable: true }) errorText: string;

  /**
   * Adds aria-label to input element. Useful for aria when no label-text or label slot is used.
   */
  @Prop() inputAriaLabel: string;

  /**
   * Adds a hidden span inside of form field and is read out after any label or help text.
   */
  @Prop() srOnly: string;

  /**
   * Can clear selected.
   */
  @Prop({ reflect: true }) clearable: boolean;

  /**
   * Shows loading indicator at end of input.
   */
  @Prop({ reflect: true, mutable: true }) loading: boolean;

  /**
   * Emitted when component focused.
   */
  @Event() guiFocused: EventEmitter<void>;

  /**
   * Emitted when component blurred.
   */
  @Event() guiBlurred: EventEmitter<void>;

  /**
   * Emitted when component receives input similar to a browser 'input' event.
   */
  @Event() guiChanged: EventEmitter<GuiChangeEvent>;

// Note: This name should have been guiChanged and guiChanged become guiInput but too many consumers already depend on guiChanged in production to rename it.
  /**
   * Emitted when component has a committed change from the user similar to a browser 'change' event.
   */
  @Event() guiCommittedChange: EventEmitter<GuiCommittedChangeEvent>;

  /**
   * Emitted when the type="date", the components value changes from a user input, the control has been checked for validity and it's constraints are not met.
   */
  @Event() guiValidityChanged: EventEmitter<GuiInvalidEvent>;

  /**
   * Emitted when the user clears the input of type search or clicks the search button.
   */
  @Event() guiSearch: EventEmitter<{ value: string }>;

  componentDidLoad() {
    listenToFormSubmit(this.host, this.inputElement);
  }

  componentWillRender() {
    this.fieldControlData = makeFieldControlData(this, this.inputId);
  }

  componentDidRender() {
    if (this.savedFocusOnLoading) {
      if (this.loading) {
        this.guiSpinner.focus();
      } else if (this.savedFocusOnLoading === 'search') {
        this.searchButton?.focus();
        this.savedFocusOnLoading = null;
      }
    }
  }

  disconnectedCallback() {
    removeListenToFormSubmit(this.host, this.inputElement);
  }

  @Watch('value')
  onValueChanged(newValue: any, oldValue: any) {
    if (newValue === oldValue || !this.inputElement) {
      return;
    }

    // Note: Updating a content attribute on the input element with <input value={this.value}/> will not update the IDL attribute (aka javascript object) of the 'input.value' or reflect the value onto the host content attribute, it will set the initial default value of the control.
    if (this.type === 'date') {
      this.checkValidityAndUpdateDateInputValue(newValue);
    } else {
      this.inputElement.value = newValue;
    }
  }

  @Watch('loading')
  onLoadingChange(newValue: any, oldValue: any) {
    if (newValue === oldValue || !this.inputElement) {
      return;
    }

    if (newValue === true && this.searchButton?.matches(':focus')) {
      this.savedFocusOnLoading = 'search';
    }
  }


  /**
   * Programmatically set component focus.
   */
  @Method()
  async guiFocus() {
    this.inputElement.focus();
  }

  /**
   * Programmatically clear the component value. This will emit a 'guiChanged' event used by form frameworks to keep their state in sync and may require guard logic to prevent recursive updates.
   */
  @Method()
  async guiClear() {
    this.clear();
  }

  private clear(emitGuiSearchAndFocusInput = false) {
    this.inputElement.value = '';
    this.value = '';
    this.guiChanged.emit({
      id: this.inputId,
      value: this.value,
    });
    if (emitGuiSearchAndFocusInput) {
      this.inputElement.focus();
      this.guiSearch.emit({ value: this.value });
    }
  }

  private onFocus = () => {
    this.hasFocus = true;
    this.guiFocused.emit();
  };

  private onBlur = () => {
    this.hasFocus = false;
    this.guiBlurred.emit();
  };

  private onInput = (event: InputEvent) => {
    if (this.disabled) {
      return;
    }

    const input = event.target as HTMLInputElement;
    this.value = input.value || '';
    this.guiChanged.emit({
      id: this.inputId,
      value: this.value,
    });
  };

  private onChange = (event: Event) => {
    if (this.disabled) {
      return;
    }

    const input = event.target as HTMLInputElement;
    this.value = input.value || '';
    this.guiCommittedChange.emit({
      value: this.value,
    });
  };

  private handleKeyDown = (event: KeyboardEvent) => {
    if (this.disabled) {
      return;
    }

    if (event.key === 'Escape' && this.value.length) {
      event.preventDefault();
      event.stopImmediatePropagation();
      this.clear();
    }

    const formElement = this.host.closest('form');
    submitOnEnter(event, formElement);
  };

  private refresh = () => {
    this.fieldControlData = makeFieldControlData(this, this.inputId);
  };

  private checkValidityAndUpdateDateInputValue(newValue: any) {
    // Note: If type="date" and the component becomes a native date picker then it will allow a user to enter a invalid date visually showing '31/02/2000', being invalid as the month of February can not have 31 days, but the value when checked will be an empty string. If the input element is invalid and the value is empty we do not set inputElement.value = newValue as this will reset the date picker making the user re-enter the whole date, instead we emit guiInvalid and let the consuming app handle the validation, which is more similar to the default behavior. Currently this is only for type="date" as since the form components where initially made the element internals standard landed in all supported browsers and the native form validation needs to be implemented to all form components.

    const isInvalid = !this.inputElement.checkValidity();
    const isEmptyNewValue = ['', null, undefined].includes(newValue);

    if (isInvalid && isEmptyNewValue) {
      const invalidStates: any = {};
      const { validity } = this.inputElement;
      /* eslint-disable-next-line */
      for (const key in validity) {
        if ((validity as any)[key] === true) {
          invalidStates[key] = true;
        }
      }
      if (invalidStates) {
        this.guiValidityChanged.emit({ validityState: invalidStates, value: newValue });
      } else {
        this.guiValidityChanged.emit({ validityState: validity, value: newValue });
      }
    } else if (!isEmptyNewValue) {
      // Note: Native browser date picker will silently fail to update it's value when given a bad input. This is work around to emit guiInvalid if setting the value fails helps consumers manage bad date inputs and issues doing custom validation with native date pickers.
      this.inputElement.value = newValue;
      const badInput = this.inputElement.value !== newValue;
      if (badInput) {
        this.guiValidityChanged.emit({ validityState: { badInput: true }, value: newValue });
        this.value = this.inputElement.value;
      }
    } else {
      this.inputElement.value = newValue;
    }
  }


  render() {
    return (
      <Host {...stripUndefinedProps({ 'data-error': this.fieldControlData.hasError })}>
        <FieldControl fieldControlData={this.fieldControlData} refresh={this.refresh}>
          <div part="input-container">
            <slot name="start"></slot>
            <slot name="inline-start"></slot>
            <input
              id={this.inputId}
              ref={input => {
                this.inputElement = input as HTMLInputElement;
              }}
              part="input"
              {...this.fieldControlData.ariaAttributes}
              {...stripUndefinedProps({
                autofocus: this.autofocus,
                autoComplete: this.autocomplete,
                name: this.name,
                placeholder: this.placeholderText,
                minLength: this.minlength,
                maxLength: this.maxlength,
                min: ['date', 'datetime-local', 'time'].includes(this.type) ? `${this.min}` : this.min,
                max: ['date', 'datetime-local', 'time'].includes(this.type) ? `${this.max}` : this.max,
                readOnly: this.disabled || this.readonly ? true : false,
                step: this.step,
                pattern: this.pattern,
                required: this.required,
                type: this.type,
              })}
              value={this.value}
              onInput={this.onInput}
              onChange={this.onChange}
              onBlur={this.onBlur}
              onFocus={this.onFocus}
              onKeyDown={this.handleKeyDown}
            />
            {(this.clearable || this.type === 'search') && !this.loading && this.value?.length > 0 && (
              <button aria-label={`Clear ${this.inputAriaLabel ? this.inputAriaLabel : this.labelText ?? ''}`} onClick={() => this.clear(true)} part="button-clear">
                <gui-icon exportparts="icon: icon-clear" name="system:input_clear"></gui-icon>
              </button>
            )}
{this.type === 'search' && !this.loading && (
              <button
                ref={button => {
                  this.searchButton = button as HTMLButtonElement;
                }}
                aria-label={`Submit ${this.inputAriaLabel ? this.inputAriaLabel : this.labelText ?? ''}`}
                onClick={() => this.guiSearch.emit({ value: this.value })}
                part="button-search"
              >
                <gui-icon exportparts="icon: icon-search" name="system:input_search"></gui-icon>
              </button>
            )}
            {this.loading && (
              <gui-spinner
                ref={guiSpinner => {
                  this.guiSpinner = guiSpinner as HTMLGuiSpinnerElement;
                }}
                role="presentation"
                aria-label="Searching indicator"
                tabindex="0"
                exportparts="spinner-container: container"
                size="small"
                loading
                loading-aria-message="Searching"
                loaded-aria-message="Search complete"
              ></gui-spinner>
            )}
            <slot name="end"></slot>
            <slot name="inline-end"></slot>
          </div>
        </FieldControl>
      </Host>
    );
  }
}