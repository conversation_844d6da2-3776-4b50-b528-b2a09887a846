import { Component, AfterViewInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { CsPhoneNumberInputComponent } from './components/cs-phone/cs-phone.component';
import { PhoneDetails, createPhoneDetails } from './components/cs-phone/cs-phone.model';

@Component({
  selector: 'app-root',
  imports: [
    RouterOutlet,
    CommonModule,
    ReactiveFormsModule,
    CsPhoneNumberInputComponent
  ],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App implements AfterViewInit {
  protected title = 'CS Phone Component Test Page';

  testForm: FormGroup;
  phoneData: PhoneDetails = createPhoneDetails();

  constructor(private fb: FormBuilder) {
    this.testForm = this.fb.group({
      phoneNumbers: [this.phoneData]
    });
  }

  ngAfterViewInit() {
    // Listen to form value changes after view initialization to avoid ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.testForm.get('phoneNumbers')?.valueChanges.subscribe(phoneDetails => {
        console.log('Phone data changed:', phoneDetails);
        this.phoneData = phoneDetails;
      });
    });
  }

  onSubmit() {
    console.log('Form submitted:', this.testForm.value);
    console.log('Phone data:', this.phoneData);
  }

  resetForm() {
    this.phoneData = createPhoneDetails();
    this.testForm.patchValue({
      phoneNumbers: this.phoneData
    });
  }

  loadSampleData() {
    this.phoneData = {
      mobile: {
        enabled: true,
        countryCode: '61',
        number: '0412345678'
      },
      dayTime: {
        enabled: true,
        countryCode: '61',
        number: '0298765432'
      },
      afterHours: {
        enabled: false,
        countryCode: '61',
        number: ''
      }
    };
    this.testForm.patchValue({
      phoneNumbers: this.phoneData
    });
  }
}
