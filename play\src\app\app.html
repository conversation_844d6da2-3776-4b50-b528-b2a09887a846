<div class="app-container">
  <header class="app-header">
    <h1>{{ title }}</h1>
    <p>This is a test page for developing and debugging the cs-phone component.</p>
  </header>

  <main class="app-main">
    <div class="test-controls">
      <h2>Test Controls</h2>
      <div class="button-group">
        <button type="button" (click)="loadSampleData()" class="btn btn-primary">
          Load Sample Data
        </button>
        <button type="button" (click)="resetForm()" class="btn btn-secondary">
          Reset Form
        </button>
        <button type="button" (click)="onSubmit()" class="btn btn-success">
          Submit Form
        </button>
        <button type="button" (click)="triggerValidation()" class="btn btn-warning">
          Trigger External Validation
        </button>
      </div>
    </div>

    <div class="component-test">
      <h2>CS Phone Component</h2>
      <form [formGroup]="testForm" (ngSubmit)="onSubmit()">
        <cs-phone
          #phoneComponent1
          formControlName="phoneControl1"
          [phoneData]="pd"
          [minRequiredPhones]="1"
          (phoneChange)="onPhoneChange($event)">

          <div slot="heading">
            <h3>Contact Phone Numbers</h3>
          </div>

          <div slot="description">
            <p>
              Please provide your contact phone numbers. At least one phone number is required.
              For Australian landline numbers, please include the area code (e.g., 02, 03, 07, 08).
            </p>
          </div>
        </cs-phone>
      </form>
    </div>

    <div class="debug-info">
      <h2>Debug Information</h2>
      <div class="debug-section">
        <h3>Form Status</h3>
        <p><strong>Valid:</strong> {{ testForm.valid }}</p>
        <p><strong>Touched:</strong> {{ testForm.touched }}</p>
        <p><strong>Dirty:</strong> {{ testForm.dirty }}</p>
      </div>

      <div class="debug-section">
        <h3>Form Value</h3>
        <pre>{{ testForm.value | json }}</pre>
      </div>

      <div class="debug-section">
        <h3>Phone Data (pd variable)</h3>
        <pre>{{ pd | json }}</pre>
      </div>

      <div class="debug-section">
        <h3>Phone Control Status</h3>
        <p><strong>Valid:</strong> {{ phoneControl1.valid }}</p>
        <p><strong>Touched:</strong> {{ phoneControl1.touched }}</p>
        <p><strong>Dirty:</strong> {{ phoneControl1.dirty }}</p>
      </div>

      <div class="debug-section">
        <h3>Form Errors</h3>
        <pre>{{ testForm.errors | json }}</pre>
      </div>
    </div>
  </main>
</div>
