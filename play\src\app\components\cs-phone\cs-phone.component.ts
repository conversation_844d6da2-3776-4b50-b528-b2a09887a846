import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  forwardRef,
  DestroyRef,
  inject,
  Input,
  SimpleChanges,
  OnChanges,
  Output,
  EventEmitter,
  ChangeDetectorRef
} from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import { GuiInputComponent } from '../gui/gui-input.component';
import { GuiSelectComponent } from '../gui/gui-select.component';
import { GuiOptionComponent } from '../gui/gui-option.component';
import { GuiCheckboxComponent } from '../gui/gui-checkbox.component';
import { GuiGridComponent } from '../gui/gui-grid.component';
import { ReferenceDataService } from '../../services/reference-data.service';
import {
  PhoneDetails,
  PhoneNumber,
  createPhoneDetails,
  PhoneFormErrorLabels,
  DialingCodeDTO
} from './cs-phone.model';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { debounceTime } from 'rxjs';
import {
  DEFAULT_PHONE_FORM_ERROR_LABELS,
  DEFAULT_PHONE_FORM_LABELS,
  DEFAULT_PHONE_DIALING_CODES
} from './cs-phone.constants';

@Component({
  selector: 'cs-phone',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GuiInputComponent,
    GuiSelectComponent,
    GuiOptionComponent,
    GuiCheckboxComponent,
    GuiGridComponent,
  ],
  templateUrl: './cs-phone.component.html',
  styleUrl: './cs-phone.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CsPhoneNumberInputComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => CsPhoneNumberInputComponent),
      multi: true,
    },
  ],
})
export class CsPhoneNumberInputComponent
  implements ControlValueAccessor, OnInit, Validator, OnChanges
{
  @Input() minRequiredPhones = 0;
  @Input() phoneData!: PhoneDetails;
  @Output() phoneChange = new EventEmitter<PhoneDetails>();
  form!: FormGroup;

  @Input() errorLabels: PhoneFormErrorLabels = DEFAULT_PHONE_FORM_ERROR_LABELS;
  @Input() friendlyNames = DEFAULT_PHONE_FORM_LABELS;

  dialingCodes: DialingCodeDTO[] = DEFAULT_PHONE_DIALING_CODES;
  DEFAULT_DIALING_CODE = '61';
  phoneTypes: (keyof PhoneDetails)[] = ['mobile', 'dayTime', 'afterHours'];
  phonePattern = /^\d+$/;

  onChange: (value: PhoneDetails | null) => void = () => { };
  onTouched: () => void = () => { };
  isDisabled = false;
  isComponentTouched = false;
  isFormSubmitted = false;
  private isInitializing = true;

  private destroyRef = inject(DestroyRef);

  constructor(
    private referenceDataService: ReferenceDataService,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef
  ) {
    this.form = this.createForm();
  }

  createForm(): FormGroup {
    return this.fb.group({
      mobile: this.createPhoneGroup(),
      dayTime: this.createPhoneGroup(),
      afterHours: this.createPhoneGroup(),
    });
  }

  createPhoneGroup(): FormGroup {
    return this.fb.group({
      enabled: [false],
      countryCode: ['', Validators.required],
      number: [
        '',
        [
          Validators.required,
          Validators.pattern(this.phonePattern),
          this.phoneNumberValidator()
        ],
      ],
    });
  }

  phoneNumberValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      const countryCode = control.parent?.get('countryCode')?.value;
      const value = control.value || '';

      if (countryCode === '61' && value.length !== 10) {
        return { australianLength: true }
      }
      if (countryCode !== '61' && value.length > 15) {
        return { internationalLength: true };
      }
      return null;
    };
  }

  ngOnInit(): void {
    this.getDialingCodes();

    if (this.phoneData) {
      this.writeValue(this.phoneData);
    }

    this.form.valueChanges
      .pipe(debounceTime(200), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        if (!this.isInitializing) {
          const sanitized = this.getSanitizedValue();
          this.phoneChange.emit(sanitized);
          this.onChange(sanitized);
          this.onTouched();
        }
      });

    // Mark initialization as complete after a short delay
    setTimeout(() => {
      this.isInitializing = false;
    });
  }

  private getSanitizedValue(): PhoneDetails {
    const sanitized = createPhoneDetails();
    this.phoneTypes.forEach(phoneType => {
      const group = this.form.get(phoneType) as FormGroup;
      const enabled = group.get('enabled')?.value;
      const valid = group.valid;

      sanitized[phoneType] = {
        enabled: enabled,
        countryCode:
          enabled && valid ? group.get('countryCode')?.value : undefined,
        number: enabled && valid ? group.get('number')?.value : undefined,
      };
    });

    return sanitized;
  }

  // control value accessor implementation
  writeValue(value: PhoneDetails | null): void {
    const val = value || createPhoneDetails();

    this.phoneTypes.forEach(phoneType => {
      const group = this.form.get(phoneType) as FormGroup;

      // if the incoming data does not have country code then preselect 61/Australia
      if (!val[phoneType]?.countryCode) {
        val[phoneType].countryCode = '61';
      }

      // Always use emitEvent: false to prevent triggering change events during writeValue
      group.patchValue(val[phoneType], { emitEvent: false });

      // Update validity without emitting events
      group.get('countryCode')?.updateValueAndValidity({ emitEvent: false });
      group.get('number')?.updateValueAndValidity({ emitEvent: false });
    });
  }

  registerOnChange(fn: (value: PhoneDetails | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = () => {
      fn();
      this.isComponentTouched = true;
      this.cdr.detectChanges();
    }
  }

  setDisabledState?(isDisabled: boolean): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }

  // validator
  validate(): ValidationErrors | null {
    const errors: ValidationErrors = {};
    let validCount = 0;

    this.phoneTypes.forEach(phoneType => {
      const phoneGroup = this.form.get(phoneType) as FormGroup;
      const phoneValue = this.form.get(phoneType)?.value;

      if (phoneValue.enabled) {
        if (phoneGroup.valid) {
          validCount++;
        } else {
          const groupErrors: ValidationErrors = {};

          const countryCodeControl = phoneGroup.get('countryCode');
          if (countryCodeControl?.errors) {
            const fieldMessages: ValidationErrors = {};
            for (const errKey in countryCodeControl.errors) {
              if (countryCodeControl.errors.hasOwnProperty(errKey)) {
                fieldMessages[errKey] = this.getMessageForSpecificError(
                  phoneType,
                  'countryCode',
                  errKey
                );
              }
            }
            if (Object.keys(fieldMessages).length > 0) {
              groupErrors['countryCode'] = fieldMessages;
            }
          }

          const numberControl = phoneGroup.get('number');
          if (numberControl?.errors) {
            const fieldMessages: ValidationErrors = {};
            for (const errKey in numberControl.errors) {
              if (numberControl.errors.hasOwnProperty(errKey)) {
                fieldMessages[errKey] = this.getMessageForSpecificError(phoneType, 'number', errKey);
              }
            }
            if (Object.keys(fieldMessages).length > 0) {
              groupErrors['number'] = fieldMessages;
            }
          }

          if (Object.keys(groupErrors).length > 0) {
            errors[phoneType] = groupErrors;
          }
          phoneGroup.markAllAsTouched();
        }
      }
    });

    if (this.minRequiredPhones > 0) {
      if (validCount < this.minRequiredPhones) {
        errors['minRequired'] = {
          required: this.minRequiredPhones,
          actual: validCount,
          message: `At least ${this.minRequiredPhones} valid phone number(s) required`,
        };
      }
    }

    return Object.keys(errors).length ? errors : null;
  }

  markAsTouched() {
    this.isComponentTouched = true;
    this.form.markAllAsTouched();
    this.cdr.detectChanges();
  }

  markAsSubmitted() {
    this.isFormSubmitted = true;
    this.isComponentTouched = true;
    this.form.markAllAsTouched();
    this.cdr.detectChanges();
  }

  markControlTouched(phoneType: keyof PhoneDetails, field: string): void {
    const control = this.form.get(`${phoneType}.${field}`);
    if (!control) return;
    control.markAsTouched();
    this.onTouched();
    this.cdr.detectChanges();
  }

  getErrorMessage(phoneType: keyof PhoneDetails, field: string): string {
    const control = this.form.get(`${phoneType}.${field}`);
    if (!control) return '';

    if (!control.errors) return '';

    // Only show errors if:
    // 1. Field is touched AND dirty (user has interacted with it), OR
    // 2. Component is marked as touched externally (e.g., form submission), OR
    // 3. The entire form has been submitted
    const shouldShowErrors =
      (control.touched && control.dirty) ||
      this.isComponentTouched ||
      this.isFormSubmitted;

    if (!shouldShowErrors)
      return '';

    const errors = control.errors || {};
    const isAustralia =
      this.form.get(`${phoneType}.countryCode`)?.value === '61';
    const phoneNumber = control.value;

    if (field === 'number') {
      if (errors['required']) {
        return this.errorLabels.required;
      }

      if (phoneNumber && /[^0-9]/.test(phoneNumber)) {
        return this.errorLabels.numbersOnly;
      }

      if (isAustralia) {
        if (phoneNumber.length !== 10) {
          return this.errorLabels.australianLength;
        } else {
          return this.errorLabels.australianLengthNonMobile;
        }
      } else if (phoneNumber.length > 15) {
        return this.errorLabels.internationalLength;
      }

      if (errors['pattern']) {
        return this.errorLabels.invalidFormat;
      }
    }

    if (field === 'countryCode') {
      if (errors['required']) {
        return this.errorLabels.required;
      }
    }

    return '';
  }

  private getMessageForSpecificError(phoneType: keyof PhoneDetails, field: string, _errorKey: string): string {
    // This method was referenced but missing in the original code
    return this.getErrorMessage(phoneType, field);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['phoneData'] && changes['phoneData'].currentValue) {
      this.writeValue(changes['phoneData'].currentValue);
      this.form.markAsTouched();
    }

    if (
      changes['minRequiredPhones'] &&
      changes['minRequiredPhones'].firstChange
    ) {
      this.onChange(this.getSanitizedValue());
    }
  }

  get formErrors() {
    return this.form.errors || '';
  }

  getControl(phoneType: keyof PhoneDetails, field: keyof PhoneNumber) {
    return this.form.get([phoneType, field]) as FormControl;
  }

  getCountryOptionLabel(c: DialingCodeDTO) {
    return `${c.countryName} (+${c.dialingCode})`;
  }

  private getDialingCodes() {
    this.referenceDataService.getDialingCodes().subscribe({
      next: (dialingCodes) => {
        if (dialingCodes) {
          const dialingCodesFiltered = dialingCodes
            .filter(_ => _.countryName && _.dialingCode)
            .sort((a, b) => a.countryName!.localeCompare(b.countryName));

          const blank = {
            countryCode: '',
            countryName: 'Select a dialing code',
            dialingCode: '',
          };
          this.dialingCodes = [blank, ...dialingCodesFiltered];
        }
      },
      error: (error) => {
        console.warn('Failed to load dialing codes, using defaults', error);
      }
    });
  }
}
