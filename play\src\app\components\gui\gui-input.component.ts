import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'gui-input',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="gui-input-wrapper">
      <input
        [type]="type"
        [value]="value"
        [disabled]="disabled"
        [placeholder]="placeholder"
        [autocomplete]="autocomplete"
        (input)="onInput($event)"
        (blur)="onBlur()"
        (focus)="onFocus()"
        class="gui-input"
      />
      <div *ngIf="errorText" class="error-text">{{ errorText }}</div>
    </div>
  `,
  styles: [`
    .gui-input-wrapper {
      margin-bottom: 1rem;
    }
    .gui-input {
      width: 100%;
      padding: 0.5rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 1rem;
    }
    .gui-input:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
    .gui-input:disabled {
      background-color: #f5f5f5;
      cursor: not-allowed;
    }
    .error-text {
      color: #dc3545;
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }
  `],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => GuiInputComponent),
      multi: true
    }
  ]
})
export class GuiInputComponent implements ControlValueAccessor {
  @Input() type: string = 'text';
  @Input() placeholder: string = '';
  @Input() disabled: boolean = false;
  @Input() errorText: string = '';
  @Input() autocomplete: string = '';
  
  @Output() guiChanged = new EventEmitter<any>();
  @Output() guiBlurred = new EventEmitter<void>();
  @Output() guiFocused = new EventEmitter<void>();

  value: string = '';
  
  onChange = (value: string) => {};
  onTouched = () => {};

  onInput(event: any) {
    this.value = event.target.value;
    this.onChange(this.value);
    this.guiChanged.emit({ value: this.value });
  }

  onBlur() {
    this.onTouched();
    this.guiBlurred.emit();
  }

  onFocus() {
    this.guiFocused.emit();
  }

  writeValue(value: string): void {
    this.value = value || '';
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}
